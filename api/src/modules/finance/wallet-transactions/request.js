import { getWalletBalance } from "finance/patients/controller";
import WalletTransaction from "finance/wallet-transactions/model";
import Patient from "frontdesk/patients/model";
import { fileValidatorSchema } from "helpers/constants";
import { error } from "helpers/response";
import { checkOTP } from "helpers/utils";
import Joi from "joi";
import { Types } from "mongoose";
import { compileEligibleVoters } from "settings/staff/helpers";

const { ObjectId } = Types;

export const createSchema = Joi.object({
    amount: Joi.number().required(),
    method: Joi.string().required().valid("cash", "pos", "transfer", "cheque", "direct-lodgement"),
    paymentReference: Joi.string().when("method", { not: "cash", then: Joi.required() }),
    provider: Joi.string().when("method", { not: "cash", then: Joi.required() }),
    comment: Joi.string()
});

export const transferSchema = Joi.object({
    from: Joi.string().required(),
    amount: Joi.number().required(),
    to: Joi.string().required().invalid(Joi.ref("from")).messages({
        "any.invalid": "Same wallet transfer is impossible. Can only transfer to other patients"
    })
});

export const confirmSchema = Joi.object({
    pin: Joi.string().required()
});

export const withdrawalSchema = Joi.object({
    amount: Joi.number().required(),
    comment: Joi.string(),
    document: fileValidatorSchema
});

export const voteSchema = Joi.object({
    yes: Joi.boolean().required(),
    comment: Joi.string().when("yes", { is: false, then: Joi.required() })
});

export const cancelFundSchema = Joi.object({
    reason: Joi.string().required()
});

const purpose = Joi.string().valid("withdrawal", "transfer", "fund", "payment", "lien-hold", "lien-release");
export const fetchSchema = Joi.object({
    from: Joi.string(),
    to: Joi.string(),
    status: Joi.string(),
    statuses: Joi.array().items(Joi.string().required()),
    type: Joi.string().valid("credit", "debit"),
    purpose: Joi.alternatives().try(Joi.array().items(purpose), purpose),
    branchId: Joi.string(),
    fromDate: Joi.date(),
    toDate: Joi.date().greater(Joi.ref("fromDate")),
    _id: Joi.string(),
    page: Joi.number(),
    limit: Joi.number()
});

// TODO: Check wallet amount
export const validateTransferRequest = async (req, res, next) => {
    try {
        const { from, to } = req.body;
        const _ids = [new ObjectId(from), new ObjectId(to)];
        const patients = await Patient.find({ _id: { $in: _ids } }).lean();
        if (!patients.length) {
            return error(res, 404, "Patients not found");
        }
        const transferrer = patients.find((patient) => patient._id.toString() === from);
        const recipient = patients.find((patient) => patient._id.toString() === to);
        if (!transferrer) {
            return error(res, 404, "Patient you are transferring from does not exist");
        }
        if (!recipient) {
            return error(res, 404, "Patient you are transferring to does not exist");
        }
        res.locals = { ...res.locals, transferrer, recipient };
        return next();
    } catch (err) {
        return error(res, 500, err);
    }
};

export const validateConfirmTransferRequest = async (req, res, next) => {
    try {
        const { pin } = req.body;
        const query = { _id: new ObjectId(req.params._id) };
        const transaction = await WalletTransaction.findOne(query)
            .populate([
                { path: "from", select: "wallet" },
                { path: "to", select: "wallet" }
            ])
            .lean();
        if (!transaction) {
            return error(res, 404, "Transaction not found");
        }
        if (transaction.pin !== pin) {
            return error(res, 400, "Incorrect OTP");
        }
        const validOTP = checkOTP(pin, transaction._id.toString());
        if (!validOTP) {
            return error(res, 400, "OTP is no longer valid. Resend OTP");
        }
        if (transaction.status !== "pending") {
            return error(res, 400, "Transaction already confirmed");
        }
        if (transaction.amount > transaction.from.wallet) {
            return error(res, 400, "Insufficient balance");
        }
        res.locals.transaction = transaction;
        return next();
    } catch (err) {
        return error(res, 500, err);
    }
};

export const validateResendOTPRequest = async (req, res, next) => {
    try {
        const query = { _id: new ObjectId(req.params._id) };
        const transaction = await WalletTransaction.findOne(query).populate("from", "phoneNumber").lean();
        if (!transaction) {
            return error(res, 404, "Transaction not found");
        }
        if (transaction.status !== "pending") {
            return error(res, 400, "Transaction already confirmed");
        }
        res.locals.transaction = transaction;
        return next();
    } catch (err) {
        return error(res, 500, err);
    }
};

export const validateWithdrawalRequest = async (req, res, next) => {
    try {
        const { _id: branchId, parentOrganizationId } = req.user.currentLocation;
        const patient = await Patient.findOne({ _id: new ObjectId(req.params._id) })
            .populate([{ path: "branchId", select: "currency" }])
            .lean();
        if (!patient) return error(res, 404, "Patient not found");
        patient.wallet = await getWalletBalance(patient._id);
        if (patient.wallet < req.body.amount) {
            return error(res, 400, "Patient balance not up to requested amount");
        }
        const permissions = ["approve-refund", "refund"];
        const eligibleVoters = await compileEligibleVoters({ branchId, parentOrganizationId, permissions });

        res.locals.eligibleVoters = eligibleVoters;
        res.locals.patient = patient;
        return next();
    } catch (err) {
        return error(res, 500, err);
    }
};

export const validateVoteRequest = async (req, res, next) => {
    try {
        const query = { _id: new ObjectId(req.params._id) };
        const transaction = await WalletTransaction.findOne(query).populate("branchId", "config").populate("from").lean();
        if (!transaction) {
            return error(res, 404, "Request record not found");
        }
        if (transaction.status !== "pending") {
            return error(res, 400, `Request already ${transaction.status}`);
        }
        const vote = transaction.votes.find(({ operator }) => operator._id === res.locals.operator);
        if (vote) {
            return error(res, 400, `You have already ${vote.yes} ? "approved" : "declined"`);
        }
        const { config } = transaction.branchId;
        res.locals = { ...res.locals, transaction, refundAuthorization: config?.refundAuthorization || 0 };
        return next();
    } catch (err) {
        return error(res, 500, err);
    }
};

export const validateProcessWithdrawalRequest = async (req, res, next) => {
    try {
        const query = { _id: new ObjectId(req.params._id) };
        const transaction = await WalletTransaction.findOne(query).populate("from", "wallet").lean();
        if (!transaction) {
            return error(res, 404, "Request record not found");
        }
        if (transaction.status !== "approved") {
            return error(res, 400, `Only approved requests can be processed. Request status: ${transaction.status}`);
        }
        const { lienBalance } = transaction.from;
        if (lienBalance < transaction.amount) {
            return error(res, 400, "Cannot process withdrawal. Insufficient lien balance");
        }
        res.locals.transaction = transaction;
        return next();
    } catch (err) {
        return error(res, 500, err);
    }
};

export const validateCreateRequest = async (req, res, next) => {
    try {
        const patient = await Patient.findById(req.params._id).lean();
        if (!patient) {
            return error(res, 404, "Patient not found");
        }
        res.locals.patient = patient;
        return next();
    } catch (err) {
        return error(res, 500, err);
    }
};

export const validateCancelFundRequest = async (req, res, next) => {
    try {
        const transaction = await WalletTransaction.findOne({ _id: new ObjectId(req.params._id) });
        if (!transaction) return error(res, 404, "Transaction does not exist");
        if (transaction.status === "cancelled") return error(res, 404, "Transaction already cancelled");
        if (transaction.purpose !== "fund" && transaction.type !== "credit") {
            return error(res, 400, "Transaction cannot be cancelled");
        }
        res.locals = { transaction };
        return next();
    } catch (err) {
        return error(res, 500, err);
    }
};
