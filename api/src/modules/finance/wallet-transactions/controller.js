// @ts-nocheck
import { paginate } from "base/helper";
import {
    composeConfirmationQueries,
    composeFilter,
    composeLienCreditDoc,
    composeLienDebitDoc,
    composeTransferTransactions,
    composeWalletDoc,
    composeWithdrawalDoc,
    sendOTP,
    sendRefundRequestEmail
} from "finance/wallet-transactions/helper";
import WalletTransaction from "finance/wallet-transactions/model";
import Patient from "frontdesk/patients/model";
import PatientService from "frontdesk/patients/service";
import { error, success } from "helpers/response";
import { generateOTP } from "helpers/utils";
import { startSession } from "mongoose";

export const fundWallet = async (req, res) => {
    try {
        let result;
        const session = await startSession();
        await session.withTransaction(async () => {
            const doc = composeWalletDoc(req, res);
            result = await new WalletTransaction(doc).save({ session });
            await Patient.findByIdAndUpdate(req.params._id, { $inc: { wallet: req.body.amount } }, { session });
        });
        await session.endSession();
        return success(res, 200, result);
    } catch (err) {
        return error(res, 500, err);
    }
};

export const cancelFund = async (req, res) => {
    const { transaction } = res.locals;
    const operator = req.user.currentLocation.profile;
    try {
        let result;
        const cancel = {
            ...req.body,
            date: new Date(),
            operator
        };
        const session = await startSession();
        await session.withTransaction(async () => {
            result = await WalletTransaction.findByIdAndUpdate(
                req.params._id,
                {
                    $set: { cancel, status: "cancelled" }
                },
                { new: true }
            );
            await Patient.findByIdAndUpdate(transaction.to, { $inc: { wallet: -transaction.amount } }, { session });
        });
        await session.endSession();
        return success(res, 200, result);
    } catch (err) {
        return error(res, 500, err);
    }
};

export const withdraw = async (req, res) => {
    try {
        let result;
        const { from } = res.locals.transaction;
        const doc = { ...req.body, openingBalance: from.wallet, closingBalance: from.wallet - req.body.amount };
        const session = await startSession();
        await session.withTransaction(async () => {
            result = await new WalletTransaction(doc).save({ session });
            await Patient.findByIdAndUpdate(req.body.patient._id, { $inc: { wallet: -req.body.amount } }, { session });
        });
        await session.endSession();
        return success(res, 200, result);
    } catch (err) {
        return error(res, 500, err);
    }
};

export const initiateTransfer = async (req, res) => {
    try {
        const transactions = composeTransferTransactions(req, res);
        const result = await WalletTransaction.insertMany(transactions);
        let transaction = result.find((record) => record.type === "debit");
        sendOTP(res.locals.transferrer.phoneNumber, transaction);
        transaction = transaction.toObject();
        delete transaction.pin;
        return success(res, 200, transaction);
    } catch (err) {
        return error(res, 500, err);
    }
};

export const confirmTransfer = async (req, res) => {
    try {
        let result;
        const { toWallet, recipientPxQuery, fromWallet, transferringPxQuery } = composeConfirmationQueries(req, res);
        const { _id, from, to, linkedTransaction } = res.locals.transaction;
        const session = await startSession();
        await session.withTransaction(async () => {
            await Patient.findByIdAndUpdate(from._id, transferringPxQuery, { session });
            await Patient.findByIdAndUpdate(to._id, recipientPxQuery, { session });
            result = await WalletTransaction.findByIdAndUpdate(_id, fromWallet, { new: true, session });
            await WalletTransaction.findByIdAndUpdate(linkedTransaction, toWallet, { session });
        });
        await session.endSession();
        return success(res, 200, result);
    } catch (err) {
        return error(res, 500, err);
    }
};

export const fetchPerPatient = async (req, res) => {
    try {
        const populate = [
            { path: "from", select: "mrn name" },
            { path: "to", select: "name mrn" }
        ];
        const result = await WalletTransaction.find({ patient: req.params._id }).populate(populate).lean();
        return success(res, 200, result);
    } catch (err) {
        return error(res, 500, err);
    }
};

export const fetch = async (req, res) => {
    try {
        const filter = composeFilter(req.query, req.user.currentLocation.parentOrganizationId);
        const { page, limit } = req.query;
        const populate = [
            { path: "from", select: "mrn name wallet" },
            { path: "to", select: "name mrn" }
        ];
        const options = { page, limit, modelName: "WalletTransaction", filter, populate };
        const data = await paginate(options);
        return success(res, 200, data);
    } catch (err) {
        return error(res, 500, err);
    }
};

export const resendOTP = async (req, res) => {
    try {
        const { transaction } = res.locals;
        const pin = generateOTP(req.params._id);
        await WalletTransaction.findByIdAndUpdate(req.params._id, { $set: { pin } });
        sendOTP(transaction.from.phoneNumber, transaction);
        return success(res, 200);
    } catch (err) {
        return error(res, 500, err);
    }
};

export const initiateWithdrawal = async (req, res) => {
    const { eligibleVoters, patient } = res.locals;
    try {
        const { amount } = req.body;
        const { _id: branchId, parentOrganizationId, profile: operator } = req.user.currentLocation;

        const session = await startSession();
        const transaction = await session.withTransaction(async () => {
            const [doc, attachment] = await composeWithdrawalDoc(req, res);
            let withdrawalTransaction = await new WalletTransaction(doc).save({ session });
            await new WalletTransaction(composeLienCreditDoc(req, res, attachment)).save({ session });
            await PatientService.update(req.params._id, {
                $inc: { wallet: -amount, lienBalance: amount }
            });
            return withdrawalTransaction;
        });
        await session.endSession();

        sendRefundRequestEmail(eligibleVoters, patient, transaction, patient.branchId.currency?.code || "NGN");
        return success(res, 200, transaction);
    } catch (err) {
        return error(res, 500, err);
    }
};

const approveWithdrawal = async (req, res) => {
    const { refundAuthorization, transaction } = res.locals;
    const noOfApprovers = transaction.votes.filter((vote) => vote.yes).length + 1;
    const status = noOfApprovers < refundAuthorization ? "pending" : "approved";
    const operator = req.user.currentLocation.profile;
    const vote = { ...req.body, date: new Date(), operator };
    const query = { $set: { status, operator }, $push: { votes: vote } };
    const result = await WalletTransaction.findByIdAndUpdate(req.params._id, query, { new: true });
    return result;
};
const declineWithdrawal = async (req, res) => {
    const { transaction } = res.locals;
    const operator = req.user.currentLocation.profile;
    const vote = { ...req.body, date: new Date(), operator };
    const query = { $set: { status: "declined", operator }, $push: { votes: vote } };    
    const session = await startSession();
    const result = await session.withTransaction(async () => {
        const result = await WalletTransaction.findByIdAndUpdate(req.params._id, query, { new: true, session });
        await new WalletTransaction(composeLienDebitDoc(req, res, false)).save({ session });
        await Patient.findByIdAndUpdate(transaction.from._id, { $inc: { wallet: transaction.amount, lienBalance: -transaction.amount } }, { session });
        return result;
    });
    await session.endSession();
    return result;
};

export const vote = async (req, res) => {
    try {
        const result = req.body.yes ? await approveWithdrawal(req, res) : await declineWithdrawal(req, res);
        return success(res, 200, result);
    } catch (err) {
        return error(res, 500, err);
    }
};

export const processWithdrawal = async (req, res) => {
    try {
        const { amount, from } = res.locals.transaction;
        const operator = req.user.currentLocation.profile;
        const openingBalance = from.wallet + amount; // Balance + lien
        const closingBalance = from.wallet;
        const walletQuery = { $set: { status: "completed", operator, openingBalance, closingBalance } };
        const patientQuery = { $inc: { lienBalance: -amount }, $set: { operator } };
        const session = await startSession();
        await session.withTransaction(async () => {
            await new WalletTransaction(composeLienDebitDoc(req, res, true)).save({ session });
            await WalletTransaction.findByIdAndUpdate(req.params._id, walletQuery, { session });
            await Patient.findByIdAndUpdate(from._id, patientQuery, { session });
        });
        await session.endSession();
        return success(res, 200);
    } catch (err) {
        return error(res, 500, err);
    }
};
