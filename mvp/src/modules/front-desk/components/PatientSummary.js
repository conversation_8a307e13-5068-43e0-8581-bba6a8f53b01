import React, { useEffect, useState } from "react";
import PropTypes from "prop-types";
import { useDispatch, useSelector } from "react-redux";
import moment from "moment";
import { fetchCurrencies } from "store/actions";
import WebcamModal from "./WebcamModal";
import {
  _firstLetterUc,
  calculateAge,
  isBilling,
  isFrontdeskPatient,
  isInPatient,
  isOutorInPatient,
  isOutPatient,
  isPharmacy,
  moneyFormater,
} from "utils";
import { CountLister, SettingsModal } from "components";
import { useCurrency } from "hooks/useCurrency";
import Allergies from "../../pharmacy/components/Allergies";
import MoreOptions from "components/MoreOptions";
import PendingBills from "../../patients/pages/Patients/PatientDetails/PendingBills";
import { NavLink } from "react-router-dom";
import { useFetchPatientsBalanceQuery } from "modules/finance/redux/features/billsSlice";
import { Edit3 } from "lucide-react";
import EditMedicalHistory, { EditFamilylHistory } from "./EditMedicalHistory";
import { EditAllergyHistory } from "./EditAllergyHistory";

export const PatientSummary = (props) => {
  const { user, match, refreshPatientSummary } = props;

  const { currency } = useCurrency();
  const { data } = useFetchPatientsBalanceQuery({ id: user._id }, { skip: !user?._id });
  const { wallet, lienBalance, pending, owing, cart } = data || {};
  const [summary, setSummary] = useState({});
  const [showWebcamModal, setShowWebcamModal] = useState(false);
  const [screenshot, setScreenshot] = useState({});
  const currentAppointment = useSelector((store) => store.currentAppointment);
  const currentAdmission = useSelector((store) => store.admissions?.currentAdmission?.items?.[0]);

  const specialty = currentAdmission?.specialty || currentAppointment?.specialty;
  const dispatch = useDispatch();

  useEffect(() => {
    dispatch(fetchCurrencies());
  }, []);

  useEffect(() => {
    setSummary({ ...user });
    return () => null;
  }, [user]);

  const { allergies = [] || "", mrn, image, title, name, patientPlan, sponsors, _id } = summary;

  const a = moment(currentAdmission?.checkout || new Date());
  const b = moment(currentAdmission?.checkInDate);
  const difference = a.diff(b, "days");

  const triggerWebcamModal = () => {
    setShowWebcamModal(!showWebcamModal);
  };
  const [isOpen, setIsOpen] = useState(false)
  const [isFamOpen, setIsFamOpen] = useState(false)
  const [isAllergyOpen, setIsAllergyOpen] = useState(false)

  const handleImageRecorvery = (imageData) => {
    setScreenshot({ ...screenshot, ...imageData });
  };

  const medicalHistory = user?.pastMedicalHistory?.filter((item) => item?.type === "medicalHistory") || [];
  const allergy = user?.pastMedicalHistory?.filter((item) => item?.type === "allergy") || [];
  const diagnosis = user?.pastMedicalHistory?.filter((item) => item?.type === "diagnosis") || [];
  const familyAndSocial = user?.pastMedicalHistory?.filter((item) => item?.type === "familyAndSocial") || [];

  const patientAge = summary?.dateOfBirth ? calculateAge(new Date(summary?.dateOfBirth)) : "-";
  return (
    <>
      <SettingsModal
        toggle={() => setIsOpen(!isOpen)}
        isOpen={isOpen}
        title="Edit Medical History"
        size="md"
        component={EditMedicalHistory}
        entity={user}
        refreshPatientSummary={refreshPatientSummary}
      />
      <SettingsModal
        toggle={() => setIsFamOpen(!isFamOpen)}
        isOpen={isFamOpen}
        title="Edit Family History"
        size="md"
        component={EditFamilylHistory}
        entity={user}
        refreshPatientSummary={refreshPatientSummary}
      />
      <SettingsModal
        toggle={() => setIsAllergyOpen(!isAllergyOpen)}
        isOpen={isAllergyOpen}
        title="Edit Allergy"
        size="md"
        component={EditAllergyHistory}
        entity={user}
        refreshPatientSummary={refreshPatientSummary}
      />
      <div className="bg-white">
        <div className="tw-flex tw-flex-wrap tw-items-start tw-justify-between patient-summary tw-pl-[2dvw] tw-pr-[5dvw]">
          <div className="tw-flex tw-justify-start tw-items-start tw-border-b tw-py-3 tw-pr-5 md:tw-border-b-0 tw-border-r-2 md:tw-border-r">
            <WebcamModal
              id={_id}
              isOpen={showWebcamModal}
              patientPlan={patientPlan}
              toggle={triggerWebcamModal}
              handleImageRecorvery={handleImageRecorvery}
              refreshPatientSummary={refreshPatientSummary}
            />
            {image || screenshot.image ? (
              <div className="avatar" onClick={triggerWebcamModal}>
                <img src={image || screenshot.avatar || ""} alt="Patient Avatar" />
              </div>
            ) : (
              <span
                className="tw-flex tw-items-center tw-justify-center color-primary tw-mr-5"
                onClick={triggerWebcamModal}
              >
                <i className="iconmoon icon-user fa-4x" />
              </span>
            )}
            <div className="details">
              <div className="tw-flex tw-justify-start tw-items-center tw-mb-3">
                <h4 className="title tw-capitalize tw-border-r tw-pr-3">{`${title || ""} ${name || "Not Provided"}`}</h4>
                <p className="paragraph-lg tw-text-[#4a4a4a] tw-pl-3">{`${mrn || "Not Provided"}`}</p>
              </div>
              <div className="tw-flex tw-items-center">
                <p className="title tw-text-sm tw-mr-3">
                  {patientAge} Yrs, {user.gender}
                </p>
                <ul className="tw-mb-1 tw-pb-3">
                  <li>
                    Plan:{" "}
                    <span className="title tw-text-sm">{_firstLetterUc(patientPlan?.name) || "Not Provided"}</span>
                  </li>
                </ul>
              </div>
              <>
                <div className="tw-flex tw-items-center tw-p-2 tw-mb-3 tw-border tw-rounded tw-border-[#ced6de] tw-bg-[#f2f4f7]">
                  <p className="provider paragraph-md tw-mt-0">Health Partners -</p>
                  {sponsors && sponsors.length > 0 ? (
                    <>
                      {" "}
                      <span className="provider-id">{`${sponsors[0].name} ${(
                        sponsors[0].plan || ""
                      ).toUpperCase()}`}</span>
                      <CountLister listOptions={sponsors} />
                    </>
                  ) : (
                    <p className="provider paragraph-sm tw-ml-2 tw-mt-0">Private Patient</p>
                  )}
                </div>
                <div className="tw-flex tw-w-fit tw-text-nowrap tw-p-2 tw-items-center tw-gap-2 tw-mb-3 tw-border tw-rounded tw-border-[#ced6de] tw-bg-[#f2f4f7]">
                  <p className="provider paragraph-lg tw-flex">
                    Wallet Balance:{" "}
                    <span className="tw-text-green tw-inline-block tw-ml-1" style={{ color: "green" }}>
                      {moneyFormater(currency, wallet, { divideBy100: true })}
                    </span>
                  </p>
                  <p className="provider paragraph-lg tw-flex">
                    Lien Balance:{" "}
                    <span className="tw-text-green tw-inline-block tw-ml-2" style={{ color: "orange" }}>
                      {moneyFormater(currency, lienBalance, { divideBy100: true })}
                    </span>
                  </p>
                </div>
                <div className="tw-flex tw-w-fit tw-items-center tw-p-2 tw-mb-3 tw-border tw-rounded tw-border-[#ced6de] tw-bg-[#f2f4f7]">
                  <p className="provider paragraph-lg tw-mt-0">
                    Credit Balance:{" "}
                    <span className="tw-text-red" style={{ color: "red" }}>
                      {moneyFormater(currency, owing, { divideBy100: true })}
                    </span>
                  </p>
                </div>
                <div className="tw-flex tw-w-fit tw-mb-3 tw-items-center tw-p-2 tw-border tw-rounded tw-border-[#ced6de] tw-bg-[#f2f4f7]">
                  <p className="provider paragraph-lg tw-mt-0 tw-pr-2">
                    Invoice Balance:{" "}
                    <span className="tw-text-green" style={{ color: "green" }}>
                      {moneyFormater(currency, pending, { divideBy100: true })}
                    </span>
                  </p>
                </div>
                <div className="tw-flex tw-w-fit tw-items-center tw-p-2 tw-border tw-rounded tw-border-[#ced6de] tw-bg-[#f2f4f7]">
                  <p className="provider paragraph-lg tw-mt-0">
                    Cart Balance:{" "}
                    <span className="tw-text-red" style={{ color: "red" }}>
                      {moneyFormater(currency, cart, { divideBy100: true })}
                    </span>
                  </p>
                </div>
              </>

              {isPharmacy() && currentAdmission?.checkInDate && !currentAdmission?.checkout && (
                <span className="tw-mt-3 access-log__badge access-log__badge--grey">{`Patient is on admission for (${difference} days)`}</span>
              )}
            </div>
          </div>
          <div className="tw-items-start tw-grow tw-py-3 tw-px-5 md:tw-border-b-0 tw-border-r-2 md:tw-border-r">
            <div className="patient-summary__provider-info lg:tw-flex lg:tw-items-start tw-space-y-5 tw-py-5">
              <div className="main-info__right tw-flex tw-flex-col tw-gap-y-7">
                {isInPatient() && (
                  <>
                    {user?.admission?.emergency === true && <div className="tw-mt-4 tag tag--red">Emergency</div>}
                    {user?.admission?.emergency === false && <div className="tw-mt-4 tag tag--red">Admission</div>}
                  </>
                )}
                {isOutorInPatient() && (
                  <>
                    <div className="tw-flex tw-flex-wrap tw-items-start tw-justify-start tw-mt-4">
                      <p className="tag tw-mr-3 tag--yellow tw-border">
                        Allergy{" "}
                        <Edit3
                          onClick={() => setIsAllergyOpen(!isAllergyOpen)}
                          size={15}
                          color="grey"
                          className="tw-ml-2 tw-cursor-pointer"
                        />
                      </p>
                      {allergy?.length > 0 ? (
                        allergy?.map((e, index) => (
                          <p
                            key={e?._id}
                            className={`tw-px-3 tw-text-[#8895A7] ${index !== allergy?.length - 1 ? "tw-border-r tw-border-r-[#EAECF0]" : ""}`}
                          >
                            {e?.name}
                          </p>
                        ))
                      ) : (
                        <span className="tw-px-3 tw-text-[#8895A7]">N/A</span>
                      )}
                    </div>
                    <div className="tw-flex tw-flex-wrap tw-justify-start tw-items-start tw-mt-4">
                      <p className="tag tw-mr-3 tag--purple tw-border">
                        Medical History{" "}
                        <Edit3
                          onClick={() => setIsOpen(!isOpen)}
                          size={15}
                          color="grey"
                          className="tw-ml-2 tw-cursor-pointer"
                        />
                      </p>
                      {medicalHistory?.length > 0 ? (
                        medicalHistory?.map((e, index) => (
                          <p
                            key={e?._id}
                            className={`tw-px-3 tw-text-[#8895A7] ${index !== medicalHistory?.length - 1 ? "tw-border-r tw-border-r-[#EAECF0]" : ""}`}
                          >
                            {e?.name}
                          </p>
                        ))
                      ) : (
                        <span className="tw-px-3 tw-text-[#8895A7]">N/A</span>
                      )}
                    </div>
                    <div className="tw-flex tw-flex-wrap tw-justify-start tw-items-start tw-mt-4">
                      <p className="tag tw-mr-3 tag--red tw-border">Previous Diagnosis </p>
                      {diagnosis?.length > 0 ? (
                        diagnosis?.map((e, index) => (
                          <p
                            key={e?._id}
                            className={`tw-px-3 tw-text-[#8895A7] ${index !== diagnosis?.length - 1 ? "tw-border-r tw-border-r-[#EAECF0]" : ""}`}
                          >
                            {e?.name}
                          </p>
                        ))
                      ) : (
                        <span className="tw-px-3 tw-text-[#8895A7]">N/A</span>
                      )}
                    </div>
                    <div className="tw-flex tw-flex-wrap tw-justify-start tw-items-start tw-mt-4">
                      <p className="tag tw-mr-3 tag--orange tw-border">
                        Family/Social{" "}
                        <Edit3
                          onClick={() => setIsFamOpen(!isFamOpen)}
                          size={15}
                          color="grey"
                          className="tw-ml-2 tw-cursor-pointer"
                        />
                      </p>
                      {familyAndSocial?.length > 0 ? (
                        familyAndSocial?.map((e, index) => (
                          <p
                            key={e?._id}
                            className={`tw-px-3 tw-text-[#8895A7] ${index !== familyAndSocial?.length - 1 ? "tw-border-r tw-border-r-[#EAECF0]" : ""}`}
                          >
                            {e?.name}
                          </p>
                        ))
                      ) : (
                        <span className="tw-px-3 tw-text-[#8895A7]">N/A</span>
                      )}
                    </div>
                  </>
                )}
              </div>
            </div>
          </div>
          <div className="tw-px-5 tw-py-3">
            <div className="patient-summary__provider-info lg:tw-flex lg:tw-items-start tw-space-y-5 tw-py-5">
              <div className="main-info__right tw-flex tw-flex-col tw-gap-y-7">
                {allergies?.length > 0 ? <Allergies allergies={allergies} /> : null}
                {isOutPatient() && (
                  <>
                    <PendingBills userInfo={user} />
                    <NavLink to={`${match.url}/archived/${user._id}`}>
                      <div className="access-log-btn m-top-15">
                        <i className="iconmoon icon-reader" />
                        Archived Records
                      </div>
                    </NavLink>
                  </>
                )}
                {!isBilling() && !isFrontdeskPatient() && !isPharmacy() && (
                  <NavLink to={`${match.url}/access-log/${user._id}`}>
                    <div className="access-log-btn">
                      <i className="iconmoon icon-reader" />
                      View Access Log
                    </div>
                  </NavLink>
                )}

                {!isPharmacy() && (
                  <MoreOptions
                    match={match}
                    userInfo={summary}
                    upload={!isBilling()}
                    specialty={specialty}
                    metaObject={{ ...currentAppointment, ...currentAdmission }}
                    refreshPatientSummary={refreshPatientSummary}
                  />
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

PatientSummary.propTypes = {
  user: PropTypes.object,
  match: PropTypes.object,
  currentAdmission: PropTypes.object,
  refreshPatientSummary: PropTypes.func,
  allergies: PropTypes.string,
};
