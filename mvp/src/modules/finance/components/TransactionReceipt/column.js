import { _firstLetterUc, _formatDate, moneyFormater } from "utils";
import moment from "moment";
import { NavLink } from "react-router-dom";
import VIewReceipt from "modules/finance/components/VIewReceipt";
import { Pill } from "components/ui/pill";
import React from "react";
import { createColumnHelper } from "@tanstack/react-table";

const columnHelper = createColumnHelper();

export const TRANSFER_RECEIPT_COLUMN_DEF = [
  columnHelper.accessor("createdAt", {
    header: "Date",
    cell: ({ row }) => {
      const { createdAt } = row.original || {};
      return (
        <div className="tw-flex tw-items-center tw-space-x-6">
          <div>
            <p className="tw-font-medium tw-text-natural-700 tw-mb-2">{_formatDate(createdAt, "DD/MM/YY")}</p>
            <p className="tw-font-normal tw-text-natural-500">{moment(createdAt).format("hh:mm A")}</p>
          </div>
        </div>
      );
    },
  }),
  columnHelper.accessor("from", {
    header: "Sender",
    cell: ({ row, table }) => {
      const { nextUrl } = table.options.meta || {};
      const { from } = row.original || {};
      return (
        <div>
          <p className="tw-font-medium tw-text-natural-700 tw-mb-2">
            <NavLink to={nextUrl(from?._id)}>{from?.name || "Not Provided"}</NavLink>
          </p>
          <span className="tw-font-normal tw-text-natural-500">{from?.mrn || "Not Provided"}</span>
        </div>
      );
    },
  }),
  columnHelper.accessor("to", {
    header: "Receiver",
    cell: ({ row, table }) => {
      const { nextUrl } = table.options.meta || {};
      const { to } = row.original || {};
      return (
        <div>
          <p className="tw-font-medium tw-text-natural-700 tw-mb-2">
            <NavLink to={nextUrl(to?._id)}>{to?.name || "Not Provided"}</NavLink>
          </p>
          <span className="tw-font-normal tw-text-natural-500">{to?.mrn || "Not Provided"}</span>
        </div>
      );
    },
  }),
  columnHelper.accessor("operator", {
    header: "Cashier",
    cell: ({ row }) => {
      const { operator } = row.original || {};
      return <p className="tw-font-normal tw-text-natural-500">{operator?.name || "Not Provided"}</p>;
    },
  }),
  columnHelper.accessor("amount", {
    header: "Amount",
    cell: ({ row, table }) => {
      const { amount } = row.original || {};
      const { currency } = table.options.meta || {};
      return (
        <p className="tw-font-normal tw-text-natural-500">
          {moneyFormater(currency, amount, { divideBy100: true }) || "NA"}
        </p>
      );
    },
  }),
  columnHelper.accessor("status", {
    header: "Status",
    cell: ({ row }) => {
      const { status } = row.original || {};
      return <Pill variant={status}>{status !== "completed" ? _firstLetterUc(status) : "Disbursed"}</Pill>;
    },
  }),
  columnHelper.accessor("viewReceipt", {
    header: " ",
    cell: ({ row }) => {
      const selected = row.original || {};
      return (
        <div className="tw-flex tw-items-center">
          <VIewReceipt title="Transfer Receipt" selected={selected} />
        </div>
      );
    },
  }),
];
