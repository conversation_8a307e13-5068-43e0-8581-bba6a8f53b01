import React, { useState } from "react";
import PropTypes from "prop-types";
import { DragAndDrop, UploadFile } from "components";
import { _notifyError, convertFile, moneyFormater, removeNonNumeric } from "utils";
import { useDispatch } from "react-redux";
import { But<PERSON> } from "components/ui/button";
import { Form, FormControl, FormField, FormItem, FormMessage } from "components/ui/form";
import { useForm } from "react-hook-form";
import { NumberField } from "components/ui/number-field";
import { Badge } from "components/ui/badge";
import { TextField } from "components/ui/text-field";
import getSymbolFromCurrency from "currency-symbol-map";
import { initiateWithdrawal } from "modules/finance/redux/actions";
import { useCurrency } from "hooks/useCurrency";
import billsApi from "../redux/features/billsSlice";

const fileTypes = ["image/jpg", "image/jpeg", "image/png", "application/pdf"];

export const WalletRefundRequest = ({ userInfo, toggle }) => {
  const dispatch = useDispatch();
  const { currency } = useCurrency();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const form = useForm({
    defaultValues: {
      amount: "",
      document: null,
      comment: "",
    },
  });

  const currencySign = getSymbolFromCurrency(currency);

  const document = form.watch("document");

  const onDeleteFile = (e) => {
    const { id } = e.target;
    const files = [...document];
    form.setValue(
      "document",
      files.filter((file) => file.binaryString !== id),
    );
  };

  const _handleFileChange = async (e, text) => {
    const fileItems = [];
    const files = text !== undefined ? e : e.target.files;
    for (let i = 0; i < files.length; i++) {
      files[i].id = i;
      files[i].binaryString = await convertFile(files[i]);
      fileItems.push(files[i]);
    }

    const filePack = Object.values(fileItems).map(({ name, binaryString, type }) => ({
      name,
      binaryString,
      type,
    }));
    if (!filePack.every((file) => fileTypes.some((fl) => fl === file.type))) {
      _notifyError("Please kindly upload image file(s) only");
      return;
    }
    const fileSizes = fileItems.map((file) => file.size);
    const getTotalFileSize = fileSizes.reduce(function (previousValue, currentValue) {
      return previousValue + currentValue;
    }, 0);

    if (getTotalFileSize > 700000) {
      _notifyError("File size too big");
      return;
    }
    form.setValue("document", filePack);
  };

  const handleSave = async (values) => {
    setIsSubmitting(true);
    const { amount, document, comment } = values;

    const newAmount = removeNonNumeric(amount);
    if (!amount || !document) {
      setIsSubmitting(false);
      _notifyError(`Please add ${!amount ? "Amount" : "Document"}`);
      return;
    }
    const payload = {
      amount: Math.floor(newAmount) * 100,
      document: document[0],
      ...(comment && { comment }),
    };

    await dispatch(
      initiateWithdrawal(userInfo?._id, payload, () => {
        setIsSubmitting(false);
        dispatch(billsApi.util.invalidateTags(['Patients-Balance']));
        toggle();
      }),
    );
    setIsSubmitting(false);
  };

  return (
    <>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleSave)}>
          <div className="tw-grid tw-gap-6">
            <div>
              <FormField
                control={form.control}
                name="amount"
                render={({ field }) => (
                  <FormItem className="tw-border-0">
                    <FormControl>
                      <NumberField
                        inputClass="tw-text-start"
                        label="Enter Amount"
                        placeholder="₦0"
                        prefix={currencySign}
                        thousandSeparator
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <Badge variant={userInfo?.wallet > 0 ? "success" : "destructive"} className="tw-mt-2">
                Wallet Balance: <span>{moneyFormater(currency, userInfo?.wallet, { divideBy100: true })}</span>
              </Badge>
            </div>
            <div className="">
              <FormField
                control={form.control}
                name="document"
                render={({ field }) => (
                  <FormItem className="tw-border-0">
                    <FormControl>
                      <DragAndDrop handleDroppedFiles={(e) => _handleFileChange(e, "dragAndDrop")}>
                        <UploadFile
                          onChange={_handleFileChange}
                          multiple
                          fileName
                          fileList={field.value}
                          onDelete={onDeleteFile}
                        />
                      </DragAndDrop>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <div className="small text-muted">You can only upload JPG PNG PDF files</div>
            </div>
            <FormField
              control={form.control}
              name="comment"
              render={({ field }) => (
                <FormItem className="tw-border-0">
                  <FormControl>
                    <TextField label="Comment" placeholder="Type comment here" autoComplete="off" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <Button type="submit" loading={isSubmitting} variant="primary" disabled={isSubmitting}>
              Request Refund
            </Button>
          </div>
        </form>
      </Form>
    </>
  );
};

WalletRefundRequest.propTypes = {
  entity: PropTypes.object,
  userInfo: PropTypes.object,
  toggle: PropTypes.func,
};
