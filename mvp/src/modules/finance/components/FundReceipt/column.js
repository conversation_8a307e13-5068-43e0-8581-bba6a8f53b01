import { _formatDate, moneyFormater } from "utils";
import moment from "moment";
import { NavLink } from "react-router-dom";
import VIewReceipt from "modules/finance/components/VIewReceipt";
import React from "react";
import { createColumnHelper } from "@tanstack/react-table";
import { ContextMenu } from "components";
import Check from "services/Check";
import { Badge } from "components/ui/badge";

const columnHelper = createColumnHelper();

const COLORS = {
  cancelled: "destructive",
  pending: "",
};
const LIEN_METHODS = ["lien-credit", "lien-debit"];

export const FUND_RECEIPT_COLUMN_DEF = [
  columnHelper.accessor("createdAt", {
    header: "Date",
    cell: ({ row }) => {
      const { createdAt } = row.original || {};
      return (
        <div className="tw-flex tw-items-center tw-space-x-6">
          <div className="">
            <p className="tw-font-medium tw-text-natural-700 tw-mb-2">{_formatDate(createdAt, "DD/MM/YY")}</p>
            <p className="tw-font-normal tw-text-natural-500">{moment(createdAt).format("hh:mm A")}</p>
          </div>
        </div>
      );
    },
  }),
  columnHelper.accessor("patientTo", {
    header: "Patient",
    cell: ({ row, table }) => {
      const { nextUrl } = table.options.meta || {};
      const { to } = row.original || {};
      return (
        <div className="">
          <p className="tw-font-medium tw-text-natural-700 tw-mb-2">
            <NavLink to={nextUrl(to?._id)}>{to?.name || "Not Provided"}</NavLink>
          </p>
          <span className="tw-font-normal tw-text-natural-500">{to?.mrn || "Not Provided"}</span>
        </div>
      );
    },
  }),
  columnHelper.accessor("cashier", {
    header: "Cashier",
    cell: ({ row }) => {
      const { operator } = row.original || {};
      return <p className="tw-font-normal tw-text-natural-500">{operator?.name || "Not Provided"}</p>;
    },
  }),
  columnHelper.accessor("amount", {
    header: "Amount",
    cell: ({ row, table }) => {
      const { amount } = row.original || {};
      const { currency } = table.options.meta || {};
      return (
        <p className="tw-font-normal tw-text-natural-500">
          {moneyFormater(currency, amount, { divideBy100: true }) || "NA"}
        </p>
      );
    },
  }),
  columnHelper.accessor("paymentType", {
    header: "Payment Type",
    cell: ({ row }) => {
      const { type } = row.original || {};
      return <p className="tw-font-normal tw-text-natural-500">{type === "credit" ? "Deposit" : "Transferred"}</p>;
    },
  }),

  columnHelper.accessor("method", {
    header: "Method",
    cell: ({ row }) => {
      const { method } = row.original || {};
      return <p className="tw-font-normal tw-text-natural-500">{method || "Transferred"}</p>;
    },
  }),
  columnHelper.accessor("status", {
    header: "Status",
    cell: ({ row }) => {
      const { status } = row.original || {};
      return (
        <div>
          <Badge variant={COLORS[status]} className={`tw-capitalize`}>
            {status}
          </Badge>
        </div>
      );
    },
  }),
  columnHelper.accessor("viewReceipt", {
    header: " ",
    cell: ({ row }) => {
      const selected = row.original || {};
      return (
        <div className="tw-flex tw-items-center">
          <VIewReceipt selected={selected} />
        </div>
      );
    },
  }),
  columnHelper.accessor("_id", {
    header: " ",
    cell: ({ table, row }) => {
      const { toggleModal, setCancelData } = table.options.meta || {};
      return (
        <div className="">
          <Check permission={["cancel-fund", "super-edit", "super-delete"]}>
            <div>
              <span className="frontdesk-context-menu color-grey ml-3" onClick={(e) => e.stopPropagation()}>
                <ContextMenu ulClass="patients-admission-card-context-menu-body">
                  <li onClick={() => toggleModal(row.original)}>Print</li>
                  {row.original.status === "completed" && !LIEN_METHODS.includes(row.original.method) && (
                    <li
                      onClick={() => {
                        setCancelData(row?.original);
                      }}
                    >
                      Cancel Funding
                    </li>
                  )}
                </ContextMenu>
              </span>
            </div>
          </Check>
        </div>
      );
    },
  }),
];
