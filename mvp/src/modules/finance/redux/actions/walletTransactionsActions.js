import { _notifyError, _notifySuccess, printError } from "utils";
import * as walletTransactionTypes from "../types/walletTransactionTypes";
import { walletApi } from "../features/walletSlice";
import Axios from "services/axios";
import { fetchWithdrawals } from "./billActions";

const fundWalletStart = () => ({
  type: walletTransactionTypes.FUND_WALLET_START,
});

const fundWalletFail = () => ({
  type: walletTransactionTypes.FUND_WALLET_FAILURE,
});

const fundWalletSuccess = () => ({
  type: walletTransactionTypes.FUND_WALLET_SUCCESS,
});

export const fundWallet = (dataId, payload, close) => async (dispatch) => {
  dispatch(fundWalletStart());
  try {
    const { data } = await Axios.post(`/patients/${dataId}/wallet-transactions`, payload);
    if (data.status) {
      _notifySuccess("Wallet is funded successfully");
      dispatch(fundWalletSuccess());
      close?.();
    }
  } catch (error) {
    const errorMessage = printError(error);
    _notifyError(errorMessage);
    dispatch(fundWalletFail());
  }
};

const fundTransferStart = () => ({
  type: walletTransactionTypes.INITIATE_FUNDS_TRANSFER_START,
});

const fundTransferFail = (payload) => ({
  type: walletTransactionTypes.INITIATE_FUNDS_TRANSFER_FAILURE,
  payload,
});

const fundTransferSuccess = (payload) => ({
  type: walletTransactionTypes.INITIATE_FUNDS_TRANSFER_SUCCESS,
  payload,
});

export const initiateFundTransfer = (payload, open) => async (dispatch) => {
  dispatch(fundTransferStart());
  try {
    const { data } = await Axios.post(`/wallet-transactions/transfers`, payload);
    if (data.status) {
      _notifySuccess("Transfer has been initiated successfully");
      dispatch(fundTransferSuccess(data.data));
      open?.();
    }
  } catch (error) {
    const errorMessage = printError(error);
    _notifyError(errorMessage);
    dispatch(fundTransferFail());
  }
};
const confirmFundTransferStart = () => ({
  type: walletTransactionTypes.CONFIRM_FUNDS_TRANSFER_START,
});

const confirmFundTransferFail = () => ({
  type: walletTransactionTypes.CONFIRM_FUNDS_TRANSFER_FAILURE,
});

const confirmFundTransferSuccess = () => ({
  type: walletTransactionTypes.CONFIRM_FUNDS_TRANSFER_SUCCESS,
});

export const confirmFundTransfer = (dataId, payload, close) => async (dispatch) => {
  dispatch(confirmFundTransferStart());
  try {
    const { data } = await Axios.post(`/wallet-transactions/${dataId}/confirm`, payload);
    if (data.status) {
      _notifySuccess("Funds has been transfered successfully");
      dispatch(confirmFundTransferSuccess());
      close?.();
    }
  } catch (error) {
    const errorMessage = printError(error);
    _notifyError(errorMessage);
    dispatch(confirmFundTransferFail());
  }
};

const resendTransferOTPStart = () => ({
  type: walletTransactionTypes.RESEND_TRANSFER_OTP_START,
});

const resendTransferOTPFail = () => ({
  type: walletTransactionTypes.RESEND_TRANSFER_OTP_FAIL,
});

const resendTransferOTPSuccess = () => ({
  type: walletTransactionTypes.RESEND_TRANSFER_OTP_SUCCESS,
});

export const resendTransferOTP = (dataId) => async (dispatch) => {
  dispatch(resendTransferOTPStart());
  try {
    const { data } = await Axios.post(`/wallet-transactions/${dataId}/resend-otp`);
    if (data.status) {
      _notifySuccess("Pin was sent successfully");
      dispatch(resendTransferOTPSuccess());
      close?.();
    }
  } catch (error) {
    const errorMessage = printError(error);
    _notifyError(errorMessage);
    dispatch(resendTransferOTPFail());
  }
};

const initiateWithdrawalStart = () => ({
  type: walletTransactionTypes.INITIATE_FUNDS_TRANSFER_START,
});

const initiateWithdrawalFail = () => ({
  type: walletTransactionTypes.INITIATE_WITHDRAWAL_FAILURE,
});

const initiateWithdrawalSuccess = () => ({
  type: walletTransactionTypes.INITIATE_WITHDRAWAL_SUCCESS,
});

export const initiateWithdrawal = (dataId, payload, close) => async (dispatch) => {
  dispatch(initiateWithdrawalStart());
  try {
    const { data } = await Axios.post(`/patients/${dataId}/withdrawals`, payload);
    if (data.status) {
      _notifySuccess("Withdrawal initiated successfully");
      dispatch(initiateWithdrawalSuccess());
      dispatch(walletApi.util.invalidateTags(['Wallet-Transactions']));
      close?.();
    }
  } catch (error) {
    const errorMessage = printError(error);
    _notifyError(errorMessage);
    dispatch(initiateWithdrawalFail());
  }
};

const confirmWithdrawalStart = () => ({
  type: walletTransactionTypes.CONFIRM_WITHDRAWAL_REQUEST_START,
});

const confirmWithdrawalFail = () => ({
  type: walletTransactionTypes.CONFIRM_WITHDRAWAL_REQUEST_FAIL,
});

const confirmWithdrawalSuccess = () => ({
  type: walletTransactionTypes.CONFIRM_WITHDRAWAL_REQUEST_SUCCESS,
});

export const confirmWithdrawal = (dataId, payload, close, branchId) => async (dispatch) => {
  dispatch(confirmWithdrawalStart());
  try {
    const { data } = await Axios.post(`/withdrawals/${dataId}`, payload);
    if (data.status) {
      _notifySuccess("Withdrawal confirmed successfully");
      dispatch(fetchWithdrawals(branchId));
      dispatch(confirmWithdrawalSuccess());
      close?.();
    }
  } catch (error) {
    const errorMessage = printError(error);
    _notifyError(errorMessage);
    dispatch(confirmWithdrawalFail());
  }
};

const processWithdrawalStart = () => ({
  type: walletTransactionTypes.PROCESS_WITHDRAWAL_START,
});

const processWithdrawalFail = () => ({
  type: walletTransactionTypes.PROCESS_WITHDRAWAL_FAIL,
});

const processWithdrawalSuccess = () => ({
  type: walletTransactionTypes.PROCESS_WITHDRAWAL_SUCCESS,
});

export const processWithdrawal = (dataId, close, branchId) => async (dispatch) => {
  dispatch(processWithdrawalStart());
  try {
    const { data } = await Axios.post(`/withdrawals/${dataId}/process`);
    if (data.status) {
      _notifySuccess("You have successfully disbursed");
      dispatch(fetchWithdrawals(branchId));
      dispatch(processWithdrawalSuccess());
      close?.();
    }
  } catch (error) {
    const errorMessage = printError(error);
    _notifyError(errorMessage);
    dispatch(processWithdrawalFail());
  }
};

const fetchAccountStatementStart = () => ({
  type: walletTransactionTypes.FETCH_ACCOUNT_STATEMENT_START,
});

const fetchAccountStatementFail = (payload) => ({
  type: walletTransactionTypes.FETCH_ACCOUNT_STATEMENT_FAIL,
  payload,
});

const fetchAccountStatementSuccess = (payload) => ({
  type: walletTransactionTypes.FETCH_ACCOUNT_STATEMENT_SUCCESS,
  payload,
});

export const fetchAccountStatement = (dataId, queryParams, open) => async (dispatch) => {
  dispatch(fetchAccountStatementStart());
  try {
    const { data } = await Axios.get(`/patients/${dataId}/account-statement${queryParams ? `?${queryParams}` : ""}`);
    if (data.status) {
      _notifySuccess("Successful");
      dispatch(fetchAccountStatementSuccess(data.data));
      open?.();
    }
  } catch (error) {
    const errorMessage = printError(error);
    _notifyError(errorMessage);
    dispatch(fetchAccountStatementFail());
  }
};
